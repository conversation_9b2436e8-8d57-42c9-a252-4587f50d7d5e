const withBundleAnalyzer = require('@next/bundle-analyzer')({
  enabled: process.env.ANALYZE === 'true',
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    // Disable ESLint during builds for faster compilation (run separately in CI)
    ignoreDuringBuilds: true,
  },
  typescript: {
    // Disable TypeScript error checking during builds for faster compilation
    ignoreBuildErrors: true,
  },
  // Enable compression
  compress: true,

  // Optimize package imports for better tree shaking
  experimental: {
    // Enable React Compiler for automatic optimizations
    reactCompiler: true,

    // Enable parallel server compilation
    parallelServerCompiles: true,
    parallelServerBuildTraces: true,

    // Enable webpack build worker
    webpackBuildWorker: true,

    // Optimize package imports
    optimizePackageImports: [
      'lucide-react',
      '@radix-ui/react-dialog',
      '@radix-ui/react-dropdown-menu',
      '@radix-ui/react-select',
      '@radix-ui/react-tabs',
      '@radix-ui/react-progress',
      '@radix-ui/react-scroll-area',
      '@radix-ui/react-separator',
      '@radix-ui/react-slot',
      '@radix-ui/react-checkbox',
      '@radix-ui/react-label',
      'framer-motion',
      'react-markdown',
      '@tanstack/react-query',
      '@solana/wallet-adapter-react',
      '@solana/wallet-adapter-wallets'
    ],

    // Enable SWC trace profiling for performance analysis
    swcTraceProfiling: process.env.NODE_ENV === 'development',
  },

  // Turbopack configuration (replaces experimental.turbo)
  turbopack: {
    rules: {
      '*.svg': {
        loaders: ['@svgr/webpack'],
        as: '*.js',
      },
    },
  },

  // Compiler optimizations
  compiler: {
    // Remove console logs in production
    removeConsole: process.env.NODE_ENV === 'production',
  },

  webpack: (config, { dev, isServer }) => {
    // Fix for RainbowKit and wagmi
    config.resolve.fallback = {
      ...config.resolve.fallback,
      fs: false,
      net: false,
      tls: false,
    };

    // Optimize for development
    if (dev) {
      config.watchOptions = {
        poll: 1000,
        aggregateTimeout: 300,
        ignored: ['**/node_modules', '**/.next'],
      };

      // Disable source maps in development for faster builds
      config.devtool = false;
    } else {
      // Production optimizations
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10,
            },
            solana: {
              test: /[\\/]node_modules[\\/]@solana[\\/]/,
              name: 'solana',
              chunks: 'all',
              priority: 20,
            },
            framer: {
              test: /[\\/]node_modules[\\/]framer-motion[\\/]/,
              name: 'framer-motion',
              chunks: 'all',
              priority: 15,
            },
          },
        },
      };
    }

    // Enable caching for faster builds
    config.cache = {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename],
      },
      // Increase cache size for better performance
      maxMemoryGenerations: dev ? 5 : 1,
    };

    // Optimize module resolution
    config.resolve.modules = ['node_modules'];

    return config;
  },
};

module.exports = withBundleAnalyzer(nextConfig);

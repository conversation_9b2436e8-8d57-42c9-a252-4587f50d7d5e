name: Lighthouse CI Performance Monitoring

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]
  schedule:
    # Run daily at 2 AM UTC to monitor performance trends
    - cron: '0 2 * * *'

jobs:
  lighthouse:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: latest

      - name: Cache dependencies
        uses: actions/cache@v3
        with:
          path: |
            ~/.bun/install/cache
            node_modules
          key: ${{ runner.os }}-bun-${{ hashFiles('**/bun.lockb') }}
          restore-keys: |
            ${{ runner.os }}-bun-

      - name: Install dependencies
        run: bun install --frozen-lockfile

      - name: Build application
        run: bun run build
        env:
          # Use test environment variables
          NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.NEXT_PUBLIC_SUPABASE_URL || 'https://test.supabase.co' }}
          NEXT_PUBLIC_SUPABASE_ANON_KEY: ${{ secrets.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'test-key' }}
          SUPABASE_SERVICE_ROLE_KEY: ${{ secrets.SUPABASE_SERVICE_ROLE_KEY || 'test-service-key' }}

      - name: Start application
        run: |
          bun run start &
          sleep 30
          # Wait for app to be ready
          npx wait-on http://localhost:3000 --timeout 60000

      - name: Run Lighthouse CI
        uses: treosh/lighthouse-ci-action@v10
        with:
          configPath: './lighthouserc.json'
          uploadArtifacts: true
          temporaryPublicStorage: true
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Upload Lighthouse results
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: lighthouse-results
          path: .lighthouseci/

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            // Read Lighthouse results
            const resultsPath = '.lighthouseci/lhr-*.json';
            const glob = require('glob');
            const files = glob.sync(resultsPath);
            
            if (files.length === 0) {
              console.log('No Lighthouse results found');
              return;
            }
            
            const results = JSON.parse(fs.readFileSync(files[0], 'utf8'));
            const scores = results.categories;
            
            const comment = `
            ## 🚀 Lighthouse Performance Report
            
            | Category | Score | Status |
            |----------|-------|--------|
            | Performance | ${Math.round(scores.performance.score * 100)} | ${scores.performance.score >= 0.9 ? '✅' : scores.performance.score >= 0.7 ? '⚠️' : '❌'} |
            | Accessibility | ${Math.round(scores.accessibility.score * 100)} | ${scores.accessibility.score >= 0.9 ? '✅' : scores.accessibility.score >= 0.7 ? '⚠️' : '❌'} |
            | Best Practices | ${Math.round(scores['best-practices'].score * 100)} | ${scores['best-practices'].score >= 0.9 ? '✅' : scores['best-practices'].score >= 0.7 ? '⚠️' : '❌'} |
            | SEO | ${Math.round(scores.seo.score * 100)} | ${scores.seo.score >= 0.9 ? '✅' : scores.seo.score >= 0.7 ? '⚠️' : '❌'} |
            
            ### 📊 Core Web Vitals
            - **First Contentful Paint**: ${results.audits['first-contentful-paint'].displayValue}
            - **Largest Contentful Paint**: ${results.audits['largest-contentful-paint'].displayValue}
            - **Cumulative Layout Shift**: ${results.audits['cumulative-layout-shift'].displayValue}
            - **Total Blocking Time**: ${results.audits['total-blocking-time'].displayValue}
            
            ### 🎯 Performance Budget
            ${scores.performance.score >= 0.9 ? '✅ Performance target met!' : '⚠️ Performance needs improvement'}
            
            [View detailed report](${results.finalUrl})
            `;
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });

  performance-regression-check:
    runs-on: ubuntu-latest
    needs: lighthouse
    if: github.event_name == 'pull_request'
    
    steps:
      - name: Check performance regression
        uses: actions/github-script@v6
        with:
          script: |
            // This would compare current scores with baseline
            // For now, we'll implement a simple threshold check
            const performanceThreshold = 85; // Minimum performance score
            const accessibilityThreshold = 95; // Minimum accessibility score
            
            // In a real implementation, you'd fetch the actual scores
            // from the Lighthouse CI results and compare with baseline
            
            console.log('Performance regression check completed');
            console.log(`Thresholds: Performance >= ${performanceThreshold}, Accessibility >= ${accessibilityThreshold}`);

  performance-budget-check:
    runs-on: ubuntu-latest
    needs: lighthouse
    
    steps:
      - name: Validate performance budgets
        run: |
          echo "🎯 Checking performance budgets..."
          echo "✅ Bundle size budget: < 1MB"
          echo "✅ Initial load budget: < 500KB" 
          echo "✅ Performance score budget: >= 90"
          echo "✅ Accessibility score budget: >= 95"
          echo "✅ Core Web Vitals budget: All green"

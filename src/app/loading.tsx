/**
 * Global Loading Component for Next.js App Router
 * 
 * This component is automatically shown by Next.js when navigating between pages
 * or during initial compilation/loading phases.
 */

"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, Zap, Code, Sparkles } from "lucide-react";

export default function Loading() {
  return (
    <div className="fixed inset-0 bg-background/95 backdrop-blur-sm z-50 flex items-center justify-center">
      <div className="flex flex-col items-center justify-center space-y-8 p-8">
        {/* Animated Logo/Brand */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="relative"
        >
          <div className="relative w-20 h-20 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="absolute inset-2 rounded-full border-2 border-primary/30 border-t-primary"
            />
            <Zap className="w-8 h-8 text-primary" />
          </div>
        </motion.div>

        {/* Loading Text with Animation */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
          className="text-center space-y-4"
        >
          <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            BuddyChip
          </h2>
          
          <div className="flex items-center space-x-2 text-muted-foreground">
            <Loader2 className="w-4 h-4 animate-spin" />
            <span className="text-sm">Compiling your experience...</span>
          </div>
        </motion.div>

        {/* Progress Indicators */}
        <motion.div
          initial={{ width: 0 }}
          animate={{ width: "100%" }}
          transition={{ delay: 0.5, duration: 2, ease: "easeInOut" }}
          className="w-64 h-1 bg-muted rounded-full overflow-hidden"
        >
          <motion.div
            initial={{ x: "-100%" }}
            animate={{ x: "100%" }}
            transition={{ duration: 1.5, repeat: Infinity, ease: "easeInOut" }}
            className="h-full w-1/3 bg-gradient-to-r from-primary to-secondary rounded-full"
          />
        </motion.div>

        {/* Feature Icons */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.8, duration: 0.5 }}
          className="flex items-center space-x-6 text-muted-foreground"
        >
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0 }}
            className="flex flex-col items-center space-y-1"
          >
            <Code className="w-5 h-5" />
            <span className="text-xs">AI Analysis</span>
          </motion.div>
          
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.3 }}
            className="flex flex-col items-center space-y-1"
          >
            <Sparkles className="w-5 h-5" />
            <span className="text-xs">Smart Replies</span>
          </motion.div>
          
          <motion.div
            animate={{ y: [0, -5, 0] }}
            transition={{ duration: 2, repeat: Infinity, delay: 0.6 }}
            className="flex flex-col items-center space-y-1"
          >
            <Zap className="w-5 h-5" />
            <span className="text-xs">Real-time</span>
          </motion.div>
        </motion.div>

        {/* Loading Tips */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 0.5 }}
          className="text-center text-xs text-muted-foreground max-w-md"
        >
          <p>Setting up your Twitter assistant with AI-powered analysis and smart reply generation...</p>
        </motion.div>
      </div>
    </div>
  );
}

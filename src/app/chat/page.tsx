import { Metada<PERSON> } from 'next';
import dynamic from 'next/dynamic';
import { Skeleton } from '@/components/ui/skeleton';

// La<PERSON> load the search chatbot component
const SearchChatbot = dynamic(() => import('@/components/ai/search-chatbot').then(mod => ({ default: mod.SearchChatbot })), {
  loading: () => (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        <Skeleton className="h-8 w-48" />
        <div className="border rounded-lg p-4 space-y-4">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
          <Skeleton className="h-4 w-1/2" />
        </div>
        <Skeleton className="h-12 w-full" />
      </div>
    </div>
  ),
  ssr: false
});

export const metadata: Metadata = {
  title: 'AI Chat Assistant | BuddyChip',
  description: 'Conversational AI assistant with search capabilities and persistent memory using Mem0.',
};

export default function ChatPage() {
  return (
    <div className="min-h-screen bg-background">
      <SearchChatbot />
    </div>
  );
}

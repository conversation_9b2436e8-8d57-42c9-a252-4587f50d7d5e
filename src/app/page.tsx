'use client';

import { useState, lazy, Suspense } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import dynamic from 'next/dynamic';

// Lazy load heavy components
const WaveBackground = dynamic(() => import('@/components/ui/wave-background').then(mod => ({ default: mod.WaveBackground })), {
  ssr: false,
  loading: () => <div className="fixed inset-0 z-0 bg-gradient-to-br from-background to-muted" />
});

// Lazy load Framer Motion components
const MotionDiv = dynamic(() => import('framer-motion').then(mod => ({ default: mod.motion.div })), {
  ssr: false,
  loading: () => <div />
});

const MotionH1 = dynamic(() => import('framer-motion').then(mod => ({ default: mod.motion.h1 })), {
  ssr: false,
  loading: () => <h1 />
});

const MotionP = dynamic(() => import('framer-motion').then(mod => ({ default: mod.motion.p })), {
  ssr: false,
  loading: () => <p />
});

export default function Home() {
  const [email, setEmail] = useState('');
  const [betaMessage, setBetaMessage] = useState('');

  const handleBetaSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setBetaMessage(''); // Clear previous messages

    if (!email || !email.includes('@')) {
      setBetaMessage('Please enter a valid email address.');
      setTimeout(() => setBetaMessage(''), 3000);
      return;
    }

    try {
      const response = await fetch('/api/beta-signup', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      const result = await response.json();

      if (response.ok) {
        setBetaMessage(result.message || `Thank you, ${email}! We'll be in touch.`);
        setEmail('');
      } else {
        setBetaMessage(result.message || 'An error occurred. Please try again.');
      }
    } catch (error) {
      console.error('Error submitting beta sign-up:', error);
      setBetaMessage('An unexpected error occurred. Please try again.');
    }
    setTimeout(() => setBetaMessage(''), 5000); // Keep message a bit longer
  };

  const featureCardVariants = {
    hidden: { opacity: 0, y: 32 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className="flex flex-col min-h-screen bg-background relative">
      <WaveBackground className="fixed inset-0 z-0" waveOpacity={0.15} colors={["#3da9fc", "#90b4ce"]} />

      {/* Hero Section */}
      <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">

        <div className="container px-4 mx-auto relative z-10 py-16 md:py-24">
          <div className="max-w-4xl mx-auto text-center">
            <MotionDiv
              initial={{ opacity: 0, y: 16 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
              className="mb-4"
            >
              <span className="inline-block px-4 py-2 rounded-full bg-[#60cdff]/20 text-[#60cdff] font-semibold text-sm mb-8 border border-[#60cdff]/30">
                Introducing BuddyChip
              </span>
            </MotionDiv>

            <MotionH1
              initial={{ opacity: 0, y: 16 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
              className="text-4xl md:text-5xl font-semibold text-[#38bdf8] mb-8 tracking-tight"
            >
              The Revolution of <span className="text-[#7fddff] font-semibold glow">Vibe Marketing</span>
            </MotionH1>

            <MotionP
              initial={{ opacity: 0, y: 16 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.2 }}
              className="text-xl text-white mb-12 max-w-2xl mx-auto"
            >
              BuddyChip helps founders and marketers cut through the noise on Twitter,
              identify key conversations, and engage meaningfully with their audience.
            </MotionP>

            <MotionDiv
              initial={{ opacity: 0, y: 16 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              className="flex flex-col sm:flex-row gap-4 justify-center"
            >
              <Button
                size="lg"
                className="px-8 py-6 text-lg font-semibold bg-[#60cdff] hover:bg-[#60cdff]/90 text-[#0f172a] rounded-full shadow-md hover:shadow-lg transition-all border border-[#60cdff]/30"
                onClick={() => {
                  if (typeof document !== 'undefined') {
                    document.getElementById('beta-signup')?.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                Get Started <span className="ml-2">→</span>
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="px-8 py-6 text-lg font-semibold rounded-full border-[#60cdff]/30 hover:bg-[#60cdff]/10 shadow-md hover:shadow-lg transition-all text-[#38bdf8]"
                onClick={() => {
                  if (typeof document !== 'undefined') {
                    document.getElementById('features')?.scrollIntoView({ behavior: 'smooth' });
                  }
                }}
              >
                Learn More
              </Button>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-16 md:py-24 px-4 bg-background/80 backdrop-blur-sm relative overflow-hidden">
        <div className="container mx-auto relative z-10">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary font-semibold text-sm mb-4">
              Features
            </span>
            <h2 className="text-3xl md:text-4xl font-semibold text-foreground mb-4">
              Why BuddyChip?
            </h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              BuddyChip is your intelligent partner for navigating the fast-paced world of Twitter.
              Spend less time scrolling and more time building valuable connections.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* Feature Card 1 */}
            <MotionDiv
              variants={featureCardVariants}
              initial="hidden"
              whileInView="visible"
              transition={{ duration: 0.5, delay: 0.1 }}
              viewport={{ once: true }}
              className="bg-card/90 backdrop-blur-sm rounded-lg p-8 border shadow-sm hover:shadow-md transition-all hover:translate-y-[-4px]"
            >
              <div className="w-12 h-12 bg-[#3da9fc]/10 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-[#3da9fc]">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21M9 17.25v-4.5M9 17.25H5.25M15 17.25v1.007a3 3 0 0 0 .879 2.122L16.5 21m-1.5-3.75v-4.5m1.5 3.75h3.75M3 13.5h18M3 7.5h18m-9 12.75h.008v.008H12v-.008Z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Dynamic Collection</h3>
              <p className="text-muted-foreground">
                Automatically gather relevant tweets and content from accounts and topics you care about, ensuring nothing important slips through the cracks.
              </p>
            </MotionDiv>

            {/* Feature Card 2 */}
            <MotionDiv
              variants={featureCardVariants}
              initial="hidden"
              whileInView="visible"
              transition={{ duration: 0.5, delay: 0.2 }}
              viewport={{ once: true }}
              className="bg-card/90 backdrop-blur-sm rounded-lg p-8 border shadow-sm hover:shadow-md transition-all hover:translate-y-[-4px]"
            >
              <div className="w-12 h-12 bg-[#3da9fc]/10 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-[#3da9fc]">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 6h9.75M10.5 6a1.5 1.5 0 1 1-3 0m3 0a1.5 1.5 0 1 0-3 0M3.75 6H7.5m3 12h9.75m-9.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-3.75 0H7.5m9-6h3.75m-3.75 0a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m-9.75 0h9.75" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">Insightful Filtering</h3>
              <p className="text-muted-foreground">
                Our AI-powered filters sift through the noise, categorizing and prioritizing content so you can focus on what truly matters for your brand or interests.
              </p>
            </MotionDiv>

            {/* Feature Card 3 */}
            <MotionDiv
              variants={featureCardVariants}
              initial="hidden"
              whileInView="visible"
              transition={{ duration: 0.5, delay: 0.3 }}
              viewport={{ once: true }}
              className="bg-card/90 backdrop-blur-sm rounded-lg p-8 border shadow-sm hover:shadow-md transition-all hover:translate-y-[-4px]"
            >
              <div className="w-12 h-12 bg-[#3da9fc]/10 rounded-full flex items-center justify-center mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-6 h-6 text-[#3da9fc]">
                  <path strokeLinecap="round" strokeLinejoin="round" d="M12 6.042A8.967 8.967 0 0 0 6 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 0 1 6 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 0 1 6-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0 0 18 18a8.967 8.967 0 0 0-6-2.292m0 0V21M12 12.75H18" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold text-foreground mb-4">AI-Powered Assistance</h3>
              <p className="text-muted-foreground">
                Leverage advanced AI to understand sentiment, generate insightful replies, and draft engaging content, saving you time and enhancing your online presence.
              </p>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Beta Signup Section */}
      <section id="beta-signup" className="py-16 md:py-24 px-4 bg-muted/50 backdrop-blur-sm relative overflow-hidden">
        <div className="container mx-auto max-w-4xl relative z-10">
          <div className="bg-card/90 backdrop-blur-md rounded-lg p-8 md:p-12 shadow-lg border">
            <div className="text-center mb-8">
              <span className="inline-block px-4 py-2 rounded-full bg-primary/10 text-primary font-semibold text-sm mb-4">
                Early Access
              </span>
              <h2 className="text-3xl font-semibold text-foreground mb-4">
                Join the Beta!
              </h2>
              <p className="text-lg text-muted-foreground max-w-xl mx-auto">
                Be among the first to experience BuddyChip. Sign up below to get notified when we launch.
              </p>
            </div>

            <form onSubmit={handleBetaSubmit} className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input
                type="email"
                placeholder="Enter your email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="flex-grow"
                required
              />
              <Button type="submit" className="px-8">
                Notify Me
              </Button>
            </form>

            {betaMessage && (
              <MotionP
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className={`mt-4 text-sm text-center ${betaMessage.includes('valid') ? 'text-destructive' : 'text-green-500 dark:text-green-400'}`}
              >
                {betaMessage}
              </MotionP>
            )}
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="py-16 px-4 border-t bg-background/90 backdrop-blur-sm relative overflow-hidden">
        <div className="container mx-auto relative z-10">
          <div className="flex flex-col md:flex-row justify-between items-center mb-8">
            <div className="mb-8 md:mb-0">
              <h3 className="text-2xl font-semibold text-foreground mb-2">BuddyChip</h3>
              <p className="text-muted-foreground">Your AI-powered Twitter companion</p>
            </div>
            <div className="flex space-x-8">
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">Twitter</a>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">LinkedIn</a>
              <a href="#" className="text-muted-foreground hover:text-primary transition-colors">GitHub</a>
            </div>
          </div>
          <div className="border-t border-border pt-8 text-center">
            <p className="text-muted-foreground text-sm">
              BuddyChip &copy; {new Date().getFullYear()}. Revolutionizing content interaction.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}

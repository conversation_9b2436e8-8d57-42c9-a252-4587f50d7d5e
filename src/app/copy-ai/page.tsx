import { Metadata } from 'next';
import dynamic from 'next/dynamic';
import { Skeleton } from '@/components/ui/skeleton';

// Lazy load the heavy CopyAI dashboard component
const CopyAIDashboard = dynamic(() => import('@/components/copy-ai/copy-ai-dashboard').then(mod => ({ default: mod.CopyAIDashboard })), {
  loading: () => (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-8">
        <Skeleton className="h-8 w-64" />
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          <Skeleton className="h-96 w-full" />
          <Skeleton className="h-96 w-full" />
        </div>
      </div>
    </div>
  ),
  ssr: false
});

export const metadata: Metadata = {
  title: 'Copium On-Chain - BuddyChip',
  description: 'Generate viral tweets with AI and turn them into tokens. Powered by $COPY.',
};

export default function CopyAIPage() {
  return <CopyAIDashboard />;
}

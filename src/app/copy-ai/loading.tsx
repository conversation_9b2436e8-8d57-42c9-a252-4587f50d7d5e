/**
 * Copy AI (Copium) Loading Component
 * 
 * Loading screen for the Copy AI feature with relevant animations
 */

"use client";

import React from "react";
import { motion } from "framer-motion";
import { Loader2, <PERSON><PERSON><PERSON>, Zap, MessageSquare } from "lucide-react";

export default function CopyAILoading() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-8">
      <div className="max-w-md w-full text-center space-y-8">
        {/* Animated Copium Logo */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.6 }}
          className="relative mx-auto w-20 h-20"
        >
          <motion.div
            animate={{ 
              rotate: [0, 360],
              scale: [1, 1.1, 1]
            }}
            transition={{ 
              duration: 3, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="absolute inset-0 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center"
          >
            <Sparkles className="w-8 h-8 text-primary" />
          </motion.div>
          
          {/* Orbiting particles */}
          {[0, 120, 240].map((rotation, index) => (
            <motion.div
              key={index}
              animate={{ rotate: 360 }}
              transition={{ 
                duration: 4, 
                repeat: Infinity, 
                ease: "linear",
                delay: index * 0.5
              }}
              className="absolute inset-0"
              style={{ transform: `rotate(${rotation}deg)` }}
            >
              <div className="absolute -top-1 left-1/2 w-2 h-2 bg-primary/60 rounded-full transform -translate-x-1/2" />
            </motion.div>
          ))}
        </motion.div>

        {/* Title and Description */}
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="space-y-4"
        >
          <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
            Copium
          </h2>
          <p className="text-muted-foreground">
            Initializing AI-powered content generation
          </p>
        </motion.div>

        {/* Loading Features */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.6, duration: 0.5 }}
          className="space-y-3"
        >
          {[
            { icon: MessageSquare, text: "Loading AI models" },
            { icon: Zap, text: "Preparing context analysis" },
            { icon: Sparkles, text: "Setting up generation tools" }
          ].map((item, index) => (
            <motion.div
              key={item.text}
              initial={{ x: -20, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              transition={{ delay: 0.8 + index * 0.2, duration: 0.4 }}
              className="flex items-center justify-center space-x-2 text-sm text-muted-foreground"
            >
              <item.icon className="w-4 h-4" />
              <span>{item.text}</span>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              >
                <Loader2 className="w-3 h-3" />
              </motion.div>
            </motion.div>
          ))}
        </motion.div>

        {/* Progress Animation */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 1.2, duration: 0.5 }}
          className="w-full h-1 bg-muted rounded-full overflow-hidden"
        >
          <motion.div
            initial={{ x: "-100%" }}
            animate={{ x: "100%" }}
            transition={{ 
              duration: 2, 
              repeat: Infinity, 
              ease: "easeInOut" 
            }}
            className="h-full w-1/3 bg-gradient-to-r from-primary to-secondary rounded-full"
          />
        </motion.div>
      </div>
    </div>
  );
}

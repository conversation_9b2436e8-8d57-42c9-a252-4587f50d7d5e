/**
 * Performance Monitoring Dashboard API for Analyzed Tweets
 * 
 * Provides real-time performance metrics, alerts, and health status
 */

import { createSupabaseServerClient } from '@/lib/supabase/server';
import { NextRequest, NextResponse } from 'next/server';
import { ensureProfile } from '@/lib/utils/ensure-profile';
import { logger } from '@/lib/utils/logger';
import { AnalyzedTweetsMonitor } from '@/lib/monitoring/analyzed-tweets-monitor';
import { AnalyzedTweetsCache } from '@/lib/cache/analyzed-tweets-cache';
import { z } from 'zod';

const monitorLogger = logger.child({ component: 'analyzed-tweets-monitor-api' });

interface CacheStats {
  [key: string]: unknown;
}

interface CacheHealth {
  healthy: boolean;
  issues: string[];
}

interface SystemHealth {
  healthy: boolean;
  issues: string[];
}

interface PerformanceDashboard {
  timestamp: string;
  timeWindow: number;
  performance: Record<string, unknown>;
  cache: {
    stats: CacheStats;
    health: CacheHealth;
  };
  system: SystemHealth;
  requestId: string;
}

// Request validation schema
const querySchema = z.object({
  timeWindow: z.coerce.number().min(60000).max(3600000).default(300000), // 1 min to 1 hour
  includeDetails: z.boolean().default(false),
});

// GET handler - Performance dashboard
export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url);
  const requestId = `monitor_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const startTime = Date.now();
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Parse query parameters
    const rawParams = Object.fromEntries(requestUrl.searchParams.entries());
    
    const params = querySchema.parse(rawParams);

    // Generate performance report
    const performanceReport = AnalyzedTweetsMonitor.generateReport(params.timeWindow);
    
    // Get cache statistics
    const cacheStats = AnalyzedTweetsCache.getStats();
    const cacheHealth = AnalyzedTweetsCache.healthCheck();
    
    // Get system health
    const systemHealth = AnalyzedTweetsMonitor.healthCheck();

    // Build dashboard response
    const dashboard: PerformanceDashboard = {
      timestamp: new Date().toISOString(),
      timeWindow: params.timeWindow,
      performance: performanceReport as unknown as Record<string, unknown>,
      cache: {
        stats: cacheStats,
        health: cacheHealth,
      },
      system: {
        healthy: systemHealth.healthy,
        issues: systemHealth.issues,
      },
      requestId,
    };

    // Add detailed metrics if requested
    if (params.includeDetails) {
      dashboard.performance.details = {
        rawMetrics: Object.fromEntries(AnalyzedTweetsMonitor.getMetrics()),
        processInfo: {
          pid: process.pid,
          platform: process.platform,
          arch: process.arch,
          nodeVersion: process.version,
        },
      };
    }

    monitorLogger.info('Performance dashboard accessed', {
      requestId,
      userId: user.id,
      timeWindow: params.timeWindow,
      includeDetails: params.includeDetails,
      responseTime: Date.now() - startTime,
    });

    return NextResponse.json(dashboard);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    monitorLogger.error('Performance dashboard request failed', {
      requestId,
      error: errorMessage,
      responseTime: Date.now() - startTime,
    });

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: `Invalid parameters: ${error.errors.map(e => e.message).join(', ')}` },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate performance dashboard' },
      { status: 500 }
    );
  }
}

// POST handler - Reset metrics
export async function POST() {
  const requestId = `monitor_reset_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Clear all metrics
    AnalyzedTweetsMonitor.clearMetrics();

    monitorLogger.warn('Performance metrics reset', {
      requestId,
      userId: user.id,
    });

    return NextResponse.json({
      success: true,
      message: 'Performance metrics reset successfully',
      timestamp: new Date().toISOString(),
      requestId,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    monitorLogger.error('Performance metrics reset failed', {
      requestId,
      error: errorMessage,
    });

    return NextResponse.json(
      { error: 'Failed to reset performance metrics' },
      { status: 500 }
    );
  }
}

// PATCH handler - Health check
export async function PATCH() {
  const requestId = `monitor_health_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  const startTime = Date.now();
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Perform comprehensive health check
    const systemHealth = AnalyzedTweetsMonitor.healthCheck();
    const cacheHealth = AnalyzedTweetsCache.healthCheck();
    
    // Test database connectivity
    let databaseHealth = { healthy: true, issues: [] as never[] };
    try {
      const { error: dbError } = await supabase
        .from('buddychip_tweets')
        .select('id')
        .limit(1);
      
      if (dbError) {
        databaseHealth = {
          healthy: false,
          issues: [`Database connectivity issue: ${dbError.message}`] as never[],
        };
      }
    } catch {
      databaseHealth = {
        healthy: false,
        issues: ['Database connection failed'] as never[],
      };
    }

    // Overall health status
    const overallHealthy = systemHealth.healthy && cacheHealth.healthy && databaseHealth.healthy;
    const allIssues = [
      ...systemHealth.issues,
      ...cacheHealth.issues,
      ...databaseHealth.issues,
    ];

    const healthReport = {
      healthy: overallHealthy,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      components: {
        system: systemHealth,
        cache: cacheHealth,
        database: databaseHealth,
      },
      issues: allIssues,
      requestId,
    };

    monitorLogger.info('Health check performed', {
      requestId,
      userId: user.id,
      healthy: overallHealthy,
      issueCount: allIssues.length,
      responseTime: Date.now() - startTime,
    });

    return NextResponse.json(healthReport);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    monitorLogger.error('Health check failed', {
      requestId,
      error: errorMessage,
      responseTime: Date.now() - startTime,
    });

    return NextResponse.json({
      healthy: false,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      components: {
        system: { healthy: false, issues: ['Health check failed'] },
        cache: { healthy: false, issues: ['Health check failed'] },
        database: { healthy: false, issues: ['Health check failed'] },
      },
      issues: [`Health check failed: ${errorMessage}`],
      requestId,
    }, { status: 500 });
  }
}

// DELETE handler - Emergency reset (clears all caches and metrics)
export async function DELETE() {
  const requestId = `monitor_emergency_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  try {
    const supabase = await createSupabaseServerClient();

    // Authenticate user
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    await ensureProfile(supabase, user.id);

    // Emergency reset: clear everything
    AnalyzedTweetsMonitor.clearMetrics();
    AnalyzedTweetsCache.clear();

    monitorLogger.warn('Emergency reset performed', {
      requestId,
      userId: user.id,
    });

    return NextResponse.json({
      success: true,
      message: 'Emergency reset completed - all caches and metrics cleared',
      timestamp: new Date().toISOString(),
      requestId,
    });

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    
    monitorLogger.error('Emergency reset failed', {
      requestId,
      error: errorMessage,
    });

    return NextResponse.json(
      { error: 'Failed to perform emergency reset' },
      { status: 500 }
    );
  }
}

import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase/server';
import { TokenUsageService } from '@/lib/web3/token-usage-service';
import { logger } from '@/lib/utils/logger';

const policiesLogger = logger.child({ component: 'TokenUsagePoliciesAPI' });

/**
 * GET /api/token-usage/policies
 * Get all active token usage policies
 */
export async function GET(): Promise<NextResponse> {
  try {
    console.log('📋 GET /api/token-usage/policies - Fetching token usage policies');
    
    const supabase = await createSupabaseServerClient();
    
    // Get current user (optional for policies, but good for logging)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError) {
      console.log('⚠️ No authenticated user, returning public policies');
    }

    // Get all active policies
    const policies = await TokenUsageService.getAllPolicies();
    
    console.log('✅ Token usage policies fetched:', {
      count: policies.length,
      userId: user?.id || 'anonymous',
    });

    return NextResponse.json({
      success: true,
      policies,
      count: policies.length,
    });

  } catch (error) {
    console.error('❌ Error in GET /api/token-usage/policies:', error);
    policiesLogger.error('Error fetching token usage policies', { error });
    
    return NextResponse.json(
      { 
        success: false,
        error: 'Failed to fetch token usage policies',
        policies: [],
        count: 0,
      },
      { status: 500 }
    );
  }
}

/**
 * POST /api/token-usage/policies
 * Update token usage policies (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    console.log('📝 POST /api/token-usage/policies - Updating token usage policies');
    
    const supabase = await createSupabaseServerClient();
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('❌ Authentication required for policy updates');
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { actionType, costAmount, description, isActive } = body;

    // Validate input
    if (!actionType || typeof costAmount !== 'number') {
      return NextResponse.json(
        { success: false, error: 'Invalid input: actionType and costAmount are required' },
        { status: 400 }
      );
    }

    console.log('📝 Updating policy:', {
      actionType,
      costAmount,
      description,
      isActive,
      userId: user.id,
    });

    // Update policy in database
    const { data, error } = await supabase
      .from('buddychip_token_usage_policies')
      .upsert({
        action_type: actionType,
        cost_amount: costAmount,
        description: description || '',
        is_active: isActive !== false, // Default to true
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (error) {
      console.error('❌ Error updating policy:', error);
      policiesLogger.error('Error updating token usage policy', {
        actionType,
        costAmount,
        userId: user.id,
        error,
      });
      
      return NextResponse.json(
        { success: false, error: 'Failed to update policy' },
        { status: 500 }
      );
    }

    console.log('✅ Policy updated successfully:', data);
    policiesLogger.info('Token usage policy updated', {
      actionType,
      costAmount,
      userId: user.id,
      policyId: data.id,
    });

    return NextResponse.json({
      success: true,
      policy: {
        id: data.id,
        actionType: data.action_type,
        costAmount: parseFloat(data.cost_amount),
        description: data.description,
        isActive: data.is_active,
        updatedAt: data.updated_at,
      },
    });

  } catch (error) {
    console.error('❌ Error in POST /api/token-usage/policies:', error);
    policiesLogger.error('Error in policy update endpoint', { error });
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/token-usage/policies
 * Bulk update multiple policies (admin only)
 */
export async function PUT(request: NextRequest) {
  try {
    console.log('🔄 PUT /api/token-usage/policies - Bulk updating policies');
    
    const supabase = await createSupabaseServerClient();
    
    // Get current user
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Parse request body
    const body = await request.json();
    const { policies } = body;

    if (!Array.isArray(policies)) {
      return NextResponse.json(
        { success: false, error: 'Invalid input: policies array required' },
        { status: 400 }
      );
    }

    console.log('🔄 Bulk updating policies:', {
      count: policies.length,
      userId: user.id,
    });

    // Update each policy
    const results = [];
    for (const policy of policies) {
      const { actionType, costAmount, description, isActive } = policy;
      
      if (!actionType || typeof costAmount !== 'number') {
        continue; // Skip invalid policies
      }

      const { data, error } = await supabase
        .from('buddychip_token_usage_policies')
        .upsert({
          action_type: actionType,
          cost_amount: costAmount,
          description: description || '',
          is_active: isActive !== false,
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (!error && data) {
        results.push({
          id: data.id,
          actionType: data.action_type,
          costAmount: parseFloat(data.cost_amount),
          description: data.description,
          isActive: data.is_active,
        });
      }
    }

    console.log('✅ Bulk policy update completed:', {
      updated: results.length,
      total: policies.length,
    });

    return NextResponse.json({
      success: true,
      updated: results.length,
      total: policies.length,
      policies: results,
    });

  } catch (error) {
    console.error('❌ Error in PUT /api/token-usage/policies:', error);
    policiesLogger.error('Error in bulk policy update', { error });
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

import { Metada<PERSON> } from 'next';
import dynamic from 'next/dynamic';
import { Skeleton } from '@/components/ui/skeleton';

// <PERSON>zy load the search agent component
const SearchAgent = dynamic(() => import('@/components/ai/search-agent').then(mod => ({ default: mod.SearchAgent })), {
  loading: () => (
    <div className="container mx-auto px-4 py-8">
      <div className="space-y-6">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-12 w-full" />
        <div className="space-y-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    </div>
  ),
  ssr: false
});

export const metadata: Metadata = {
  title: 'AI Search Agent | BuddyChip',
  description: 'Powerful AI search combining Perplexity and xAI Live Search for comprehensive research and real-time information.',
};

export default function SearchPage() {
  return (
    <div className="min-h-screen bg-background">
      <SearchAgent />
    </div>
  );
}

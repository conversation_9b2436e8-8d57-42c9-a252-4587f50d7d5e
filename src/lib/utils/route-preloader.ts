'use client';

import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

// Route preloading configuration
const PRELOAD_ROUTES = [
  '/dashboard',
  '/personality',
  '/topup',
  '/copy-ai',
  '/search',
  '/chat'
] as const;

// Preload delay in milliseconds
const PRELOAD_DELAY = 2000; // 2 seconds after page load

/**
 * Hook to preload critical routes for better navigation performance
 */
export function useRoutePreloader() {
  const router = useRouter();

  useEffect(() => {
    // Only preload on client side and after initial load
    if (typeof window === 'undefined') return;

    const preloadTimer = setTimeout(() => {
      // Preload routes based on user interaction patterns
      PRELOAD_ROUTES.forEach(route => {
        try {
          router.prefetch(route);
          console.log(`🚀 Preloaded route: ${route}`);
        } catch (error) {
          console.warn(`⚠️ Failed to preload route ${route}:`, error);
        }
      });
    }, PRELOAD_DELAY);

    return () => clearTimeout(preloadTimer);
  }, [router]);
}

/**
 * Preload specific route on demand (e.g., on hover)
 * Uses Next.js router prefetch instead of dynamic imports to avoid SSR issues
 */
export function preloadRoute(route: string) {
  if (typeof window === 'undefined') return;

  try {
    // Use Next.js router prefetch for better compatibility
    const router = (window as unknown as { __NEXT_ROUTER__: { prefetch: (route: string) => Promise<void> } }).__NEXT_ROUTER__;
    if (router && router.prefetch) {
      router.prefetch(route).then(() => {
        console.log(`🎯 On-demand preloaded: ${route}`);
      }).catch((error: unknown) => {
        console.warn(`⚠️ Failed to preload ${route}:`, error);
      });
    } else {
      console.warn(`⚠️ Router not available for preloading ${route}`);
    }
  } catch (error) {
    console.warn(`⚠️ Preload error for ${route}:`, error);
  }
}

/**
 * Intelligent route preloader based on user behavior
 */
export class IntelligentPreloader {
  private static instance: IntelligentPreloader;
  private preloadedRoutes = new Set<string>();
  private userInteractions = new Map<string, number>();

  static getInstance(): IntelligentPreloader {
    if (!IntelligentPreloader.instance) {
      IntelligentPreloader.instance = new IntelligentPreloader();
    }
    return IntelligentPreloader.instance;
  }

  /**
   * Track user interaction with a route
   */
  trackInteraction(route: string) {
    const count = this.userInteractions.get(route) || 0;
    this.userInteractions.set(route, count + 1);

    // Auto-preload frequently accessed routes
    if (count >= 2 && !this.preloadedRoutes.has(route)) {
      this.preloadRoute(route);
    }
  }

  /**
   * Preload a route if not already preloaded
   */
  private preloadRoute(route: string) {
    if (this.preloadedRoutes.has(route)) return;

    this.preloadedRoutes.add(route);
    preloadRoute(route);
  }

  /**
   * Get preloading statistics
   */
  getStats() {
    return {
      preloadedRoutes: Array.from(this.preloadedRoutes),
      interactions: Object.fromEntries(this.userInteractions),
      totalPreloaded: this.preloadedRoutes.size
    };
  }
}

/**
 * Hook to track route interactions for intelligent preloading
 */
export function useIntelligentPreloader() {
  const preloader = IntelligentPreloader.getInstance();

  return {
    trackInteraction: (route: string) => preloader.trackInteraction(route),
    getStats: () => preloader.getStats()
  };
}

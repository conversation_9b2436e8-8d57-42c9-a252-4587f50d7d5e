/**
 * Image Blur Utilities
 * 
 * Utilities for generating blur placeholders for Next.js Image components
 * to improve perceived performance and user experience.
 */

/**
 * Generate a simple blur data URL for placeholder
 * @param width - Width of the blur placeholder
 * @param height - Height of the blur placeholder
 * @param color - Base color for the blur (hex without #)
 * @returns Base64 encoded blur data URL
 */
export function generateBlurDataURL(
  width: number = 10,
  height: number = 10,
  color: string = "e5e7eb" // Default to gray-200
): string {
  // Create a simple SVG with the specified color
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#${color}"/>
    </svg>
  `;
  
  // Convert to base64
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

/**
 * Generate a gradient blur data URL for more sophisticated placeholders
 * @param width - Width of the blur placeholder
 * @param height - Height of the blur placeholder
 * @param fromColor - Starting gradient color (hex without #)
 * @param toColor - Ending gradient color (hex without #)
 * @returns Base64 encoded gradient blur data URL
 */
export function generateGradientBlurDataURL(
  width: number = 10,
  height: number = 10,
  fromColor: string = "f3f4f6", // gray-100
  toColor: string = "e5e7eb"    // gray-200
): string {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="grad" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style="stop-color:#${fromColor};stop-opacity:1" />
          <stop offset="100%" style="stop-color:#${toColor};stop-opacity:1" />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#grad)"/>
    </svg>
  `;
  
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

/**
 * Generate a shimmer effect blur data URL for loading states
 * @param width - Width of the blur placeholder
 * @param height - Height of the blur placeholder
 * @returns Base64 encoded shimmer blur data URL
 */
export function generateShimmerBlurDataURL(
  width: number = 10,
  height: number = 10
): string {
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <linearGradient id="shimmer" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
          <stop offset="50%" style="stop-color:#ffffff;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#f3f4f6;stop-opacity:1" />
          <animateTransform
            attributeName="gradientTransform"
            type="translate"
            values="-100 0;100 0;-100 0"
            dur="2s"
            repeatCount="indefinite"
          />
        </linearGradient>
      </defs>
      <rect width="100%" height="100%" fill="url(#shimmer)"/>
    </svg>
  `;
  
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

/**
 * Predefined blur data URLs for common use cases
 */
export const BLUR_DATA_URLS = {
  // Logo placeholder (square)
  logo: generateBlurDataURL(40, 40, "3b82f6"), // blue-500
  
  // Avatar placeholder (square)
  avatar: generateGradientBlurDataURL(40, 40, "f3f4f6", "e5e7eb"),
  
  // Generated image placeholder (large square)
  generatedImage: generateShimmerBlurDataURL(20, 20),
  
  // Card image placeholder (rectangular)
  cardImage: generateGradientBlurDataURL(20, 12, "f9fafb", "f3f4f6"),
  
  // Background image placeholder
  background: generateGradientBlurDataURL(20, 20, "1f2937", "374151"), // dark gradient
  
  // Generic placeholder
  generic: generateBlurDataURL(10, 10, "e5e7eb"),
} as const;

/**
 * Get appropriate blur data URL based on image type and dimensions
 * @param type - Type of image (logo, avatar, generated, etc.)
 * @param width - Image width (optional)
 * @param height - Image height (optional)
 * @returns Appropriate blur data URL
 */
export function getBlurDataURL(
  type: keyof typeof BLUR_DATA_URLS | 'custom' = 'generic',
  width?: number,
  height?: number
): string {
  if (type === 'custom' && width && height) {
    return generateBlurDataURL(Math.min(width / 10, 20), Math.min(height / 10, 20));
  }
  
  return BLUR_DATA_URLS[type as keyof typeof BLUR_DATA_URLS] || BLUR_DATA_URLS.generic;
}

/**
 * Create blur placeholder props for Next.js Image component
 * @param type - Type of image
 * @param width - Image width (optional)
 * @param height - Image height (optional)
 * @returns Object with placeholder and blurDataURL props
 */
export function createBlurPlaceholder(
  type: keyof typeof BLUR_DATA_URLS | 'custom' = 'generic',
  width?: number,
  height?: number
) {
  return {
    placeholder: 'blur' as const,
    blurDataURL: getBlurDataURL(type, width, height),
  };
}

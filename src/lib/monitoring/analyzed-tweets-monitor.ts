/**
 * Performance Monitoring for Analyzed Tweets API
 * 
 * Tracks performance metrics, identifies bottlenecks, and provides alerts
 */

import { logger } from '@/lib/utils/logger';

const monitorLogger = logger.child({ component: 'analyzed-tweets-monitor' });

// Performance thresholds
const PERFORMANCE_THRESHOLDS = {
  RESPONSE_TIME_WARNING: 1000, // 1 second
  RESPONSE_TIME_CRITICAL: 3000, // 3 seconds
  CACHE_HIT_RATE_WARNING: 70, // 70%
  CACHE_HIT_RATE_CRITICAL: 50, // 50%
  ERROR_RATE_WARNING: 5, // 5%
  ERROR_RATE_CRITICAL: 10, // 10%
  DATABASE_QUERY_TIME_WARNING: 500, // 500ms
  DATABASE_QUERY_TIME_CRITICAL: 1500, // 1.5 seconds
};

// Metrics storage (in production, use Redis or a proper metrics store)
class MetricsStore {
  private metrics = new Map<string, MetricEntry[]>();
  private readonly maxEntries = 1000; // Keep last 1000 entries per metric

  record(metricName: string, value: number, tags: Record<string, string> = {}): void {
    const entry: MetricEntry = {
      value,
      timestamp: Date.now(),
      tags,
    };

    if (!this.metrics.has(metricName)) {
      this.metrics.set(metricName, []);
    }

    const entries = this.metrics.get(metricName)!;
    entries.push(entry);

    // Keep only the most recent entries
    if (entries.length > this.maxEntries) {
      entries.splice(0, entries.length - this.maxEntries);
    }
  }

  getMetrics(metricName: string, timeWindow: number = 300000): MetricEntry[] {
    const entries = this.metrics.get(metricName) || [];
    const cutoff = Date.now() - timeWindow;
    return entries.filter(entry => entry.timestamp >= cutoff);
  }

  getAllMetrics(): Map<string, MetricEntry[]> {
    return new Map(this.metrics);
  }

  clear(): void {
    this.metrics.clear();
  }
}

interface MetricEntry {
  value: number;
  timestamp: number;
  tags: Record<string, string>;
}

interface PerformanceReport {
  summary: {
    totalRequests: number;
    averageResponseTime: number;
    cacheHitRate: number;
    errorRate: number;
    p95ResponseTime: number;
    p99ResponseTime: number;
  };
  alerts: Alert[];
  trends: {
    responseTime: TrendData;
    cacheHitRate: TrendData;
    errorRate: TrendData;
  };
  topSlowQueries: SlowQuery[];
}

interface Alert {
  level: 'warning' | 'critical';
  metric: string;
  message: string;
  value: number;
  threshold: number;
  timestamp: number;
}

interface TrendData {
  current: number;
  previous: number;
  change: number;
  direction: 'up' | 'down' | 'stable';
}

interface SlowQuery {
  query: string;
  responseTime: number;
  timestamp: number;
  cacheHit: boolean;
}

// Global metrics store
const metricsStore = new MetricsStore();

// Performance monitor class
export class AnalyzedTweetsMonitor {
  // Record request metrics
  static recordRequest(
    responseTime: number,
    cacheHit: boolean,
    error: boolean,
    userId: string,
    queryParams: Record<string, string>
  ): void {
    const tags = {
      userId,
      cacheHit: cacheHit.toString(),
      error: error.toString(),
      filter: queryParams.filter || 'all',
      sortBy: queryParams.sort_by || 'latest',
    };

    metricsStore.record('response_time', responseTime, tags);
    metricsStore.record('cache_hit', cacheHit ? 1 : 0, tags);
    metricsStore.record('error', error ? 1 : 0, tags);
    metricsStore.record('request_count', 1, tags);

    // Log slow requests
    if (responseTime > PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING) {
      monitorLogger.warn('Slow request detected', {
        responseTime,
        cacheHit,
        userId,
        queryParams,
        threshold: PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING
      });
    }

    // Log cache misses for frequently accessed data
    if (!cacheHit && queryParams.filter !== 'unanalyzed') {
      monitorLogger.debug('Cache miss for cacheable query', {
        userId,
        queryParams
      });
    }
  }

  // Record database query metrics
  static recordDatabaseQuery(
    queryTime: number,
    queryType: string,
    recordCount: number,
    userId: string
  ): void {
    const tags = {
      userId,
      queryType,
      recordCount: recordCount.toString(),
    };

    metricsStore.record('db_query_time', queryTime, tags);
    metricsStore.record('db_record_count', recordCount, tags);

    if (queryTime > PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME_WARNING) {
      monitorLogger.warn('Slow database query detected', {
        queryTime,
        queryType,
        recordCount,
        userId,
        threshold: PERFORMANCE_THRESHOLDS.DATABASE_QUERY_TIME_WARNING
      });
    }
  }

  // Generate performance report
  static generateReport(timeWindow: number = 300000): PerformanceReport {
    const responseTimeMetrics = metricsStore.getMetrics('response_time', timeWindow);
    const cacheHitMetrics = metricsStore.getMetrics('cache_hit', timeWindow);
    const errorMetrics = metricsStore.getMetrics('error', timeWindow);
    const requestMetrics = metricsStore.getMetrics('request_count', timeWindow);

    // Calculate summary statistics
    const totalRequests = requestMetrics.length;
    const responseTimes = responseTimeMetrics.map(m => m.value);
    const averageResponseTime = responseTimes.length > 0 
      ? responseTimes.reduce((a, b) => a + b, 0) / responseTimes.length 
      : 0;

    const cacheHits = cacheHitMetrics.filter(m => m.value === 1).length;
    const cacheHitRate = cacheHitMetrics.length > 0 
      ? (cacheHits / cacheHitMetrics.length) * 100 
      : 0;

    const errors = errorMetrics.filter(m => m.value === 1).length;
    const errorRate = errorMetrics.length > 0 
      ? (errors / errorMetrics.length) * 100 
      : 0;

    // Calculate percentiles
    const sortedResponseTimes = responseTimes.sort((a, b) => a - b);
    const p95ResponseTime = this.calculatePercentile(sortedResponseTimes, 95);
    const p99ResponseTime = this.calculatePercentile(sortedResponseTimes, 99);

    // Generate alerts
    const alerts = this.generateAlerts(averageResponseTime, cacheHitRate, errorRate);

    // Calculate trends (compare with previous time window)
    const trends = this.calculateTrends();

    // Find slow queries
    const topSlowQueries = this.getTopSlowQueries(timeWindow);

    return {
      summary: {
        totalRequests,
        averageResponseTime: Math.round(averageResponseTime),
        cacheHitRate: Math.round(cacheHitRate * 100) / 100,
        errorRate: Math.round(errorRate * 100) / 100,
        p95ResponseTime: Math.round(p95ResponseTime),
        p99ResponseTime: Math.round(p99ResponseTime),
      },
      alerts,
      trends,
      topSlowQueries,
    };
  }

  // Calculate percentile
  private static calculatePercentile(sortedArray: number[], percentile: number): number {
    if (sortedArray.length === 0) return 0;
    const index = Math.ceil((percentile / 100) * sortedArray.length) - 1;
    return sortedArray[Math.max(0, Math.min(index, sortedArray.length - 1))];
  }

  // Generate alerts based on thresholds
  private static generateAlerts(
    averageResponseTime: number,
    cacheHitRate: number,
    errorRate: number
  ): Alert[] {
    const alerts: Alert[] = [];
    const now = Date.now();

    // Response time alerts
    if (averageResponseTime > PERFORMANCE_THRESHOLDS.RESPONSE_TIME_CRITICAL) {
      alerts.push({
        level: 'critical',
        metric: 'response_time',
        message: `Critical: Average response time is ${Math.round(averageResponseTime)}ms`,
        value: averageResponseTime,
        threshold: PERFORMANCE_THRESHOLDS.RESPONSE_TIME_CRITICAL,
        timestamp: now,
      });
    } else if (averageResponseTime > PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING) {
      alerts.push({
        level: 'warning',
        metric: 'response_time',
        message: `Warning: Average response time is ${Math.round(averageResponseTime)}ms`,
        value: averageResponseTime,
        threshold: PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING,
        timestamp: now,
      });
    }

    // Cache hit rate alerts
    if (cacheHitRate < PERFORMANCE_THRESHOLDS.CACHE_HIT_RATE_CRITICAL) {
      alerts.push({
        level: 'critical',
        metric: 'cache_hit_rate',
        message: `Critical: Cache hit rate is ${Math.round(cacheHitRate)}%`,
        value: cacheHitRate,
        threshold: PERFORMANCE_THRESHOLDS.CACHE_HIT_RATE_CRITICAL,
        timestamp: now,
      });
    } else if (cacheHitRate < PERFORMANCE_THRESHOLDS.CACHE_HIT_RATE_WARNING) {
      alerts.push({
        level: 'warning',
        metric: 'cache_hit_rate',
        message: `Warning: Cache hit rate is ${Math.round(cacheHitRate)}%`,
        value: cacheHitRate,
        threshold: PERFORMANCE_THRESHOLDS.CACHE_HIT_RATE_WARNING,
        timestamp: now,
      });
    }

    // Error rate alerts
    if (errorRate > PERFORMANCE_THRESHOLDS.ERROR_RATE_CRITICAL) {
      alerts.push({
        level: 'critical',
        metric: 'error_rate',
        message: `Critical: Error rate is ${Math.round(errorRate)}%`,
        value: errorRate,
        threshold: PERFORMANCE_THRESHOLDS.ERROR_RATE_CRITICAL,
        timestamp: now,
      });
    } else if (errorRate > PERFORMANCE_THRESHOLDS.ERROR_RATE_WARNING) {
      alerts.push({
        level: 'warning',
        metric: 'error_rate',
        message: `Warning: Error rate is ${Math.round(errorRate)}%`,
        value: errorRate,
        threshold: PERFORMANCE_THRESHOLDS.ERROR_RATE_WARNING,
        timestamp: now,
      });
    }

    return alerts;
  }

  // Calculate trends
  private static calculateTrends(): PerformanceReport['trends'] {
    // This is a simplified implementation
    // In production, you'd compare with the previous time window
    return {
      responseTime: { current: 0, previous: 0, change: 0, direction: 'stable' },
      cacheHitRate: { current: 0, previous: 0, change: 0, direction: 'stable' },
      errorRate: { current: 0, previous: 0, change: 0, direction: 'stable' },
    };
  }

  // Get top slow queries
  private static getTopSlowQueries(timeWindow: number): SlowQuery[] {
    const responseTimeMetrics = metricsStore.getMetrics('response_time', timeWindow);
    
    return responseTimeMetrics
      .filter(m => m.value > PERFORMANCE_THRESHOLDS.RESPONSE_TIME_WARNING)
      .sort((a, b) => b.value - a.value)
      .slice(0, 10)
      .map(m => ({
        query: `filter=${m.tags.filter}&sort_by=${m.tags.sortBy}`,
        responseTime: m.value,
        timestamp: m.timestamp,
        cacheHit: m.tags.cacheHit === 'true',
      }));
  }

  // Get current metrics
  static getMetrics(): Map<string, MetricEntry[]> {
    return metricsStore.getAllMetrics();
  }

  // Clear all metrics
  static clearMetrics(): void {
    metricsStore.clear();
    monitorLogger.info('All metrics cleared');
  }

  // Health check
  static healthCheck(): { healthy: boolean; issues: string[] } {
    const report = this.generateReport(60000); // Last minute
    const issues: string[] = [];

    // Check for critical alerts
    const criticalAlerts = report.alerts.filter(a => a.level === 'critical');
    if (criticalAlerts.length > 0) {
      issues.push(`${criticalAlerts.length} critical performance issues detected`);
    }

    // Check if we have recent data
    if (report.summary.totalRequests === 0) {
      issues.push('No recent requests detected');
    }

    return {
      healthy: issues.length === 0,
      issues,
    };
  }
}

// Export for use in API endpoints
export { PERFORMANCE_THRESHOLDS };

/**
 * Comprehensive Validation Middleware
 *
 * Provides centralized input validation, XSS protection, and SQL injection prevention
 * for all API endpoints. Includes request sanitization and security logging.
 */

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { logger } from '@/lib/utils/logger';

// Security patterns for detection
const SECURITY_PATTERNS = {
  XSS: [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /<object[^>]*>.*?<\/object>/gi,
    /<embed[^>]*>.*?<\/embed>/gi,
  ],
  SQL_INJECTION: [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
    /(--|\/\*|\*\/|;|'|"|`)/g,
    /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
    /\b(UNION|SELECT)\b.*\b(FROM|WHERE)\b/gi,
  ],
  PATH_TRAVERSAL: [
    /\.\.\//g,
    /\.\.\\/g,
    /%2e%2e%2f/gi,
    /%2e%2e%5c/gi,
  ],
  COMMAND_INJECTION: [
    /[;&|`$(){}[\]]/g,
    /\b(eval|exec|system|shell_exec|passthru)\b/gi,
  ],
};

// Security threat levels
enum ThreatLevel {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical'
}

interface SecurityThreat {
  type: string;
  pattern: string;
  level: ThreatLevel;
  field?: string;
  value?: string;
}

/**
 * Detect security threats in input data
 */
function detectSecurityThreats(data: unknown, path = ''): SecurityThreat[] {
  const threats: SecurityThreat[] = [];

  if (typeof data === 'string') {
    // Check for XSS
    SECURITY_PATTERNS.XSS.forEach(pattern => {
      if (pattern.test(data)) {
        threats.push({
          type: 'XSS',
          pattern: pattern.source,
          level: ThreatLevel.HIGH,
          field: path,
          value: data.substring(0, 100)
        });
      }
    });

    // Check for SQL injection
    SECURITY_PATTERNS.SQL_INJECTION.forEach(pattern => {
      if (pattern.test(data)) {
        threats.push({
          type: 'SQL_INJECTION',
          pattern: pattern.source,
          level: ThreatLevel.CRITICAL,
          field: path,
          value: data.substring(0, 100)
        });
      }
    });

    // Check for path traversal
    SECURITY_PATTERNS.PATH_TRAVERSAL.forEach(pattern => {
      if (pattern.test(data)) {
        threats.push({
          type: 'PATH_TRAVERSAL',
          pattern: pattern.source,
          level: ThreatLevel.MEDIUM,
          field: path,
          value: data.substring(0, 100)
        });
      }
    });

    // Check for command injection
    SECURITY_PATTERNS.COMMAND_INJECTION.forEach(pattern => {
      if (pattern.test(data)) {
        threats.push({
          type: 'COMMAND_INJECTION',
          pattern: pattern.source,
          level: ThreatLevel.HIGH,
          field: path,
          value: data.substring(0, 100)
        });
      }
    });
  } else if (typeof data === 'object' && data !== null) {
    // Recursively check object properties
    Object.entries(data).forEach(([key, value]) => {
      threats.push(...detectSecurityThreats(value, path ? `${path}.${key}` : key));
    });
  }

  return threats;
}

/**
 * Sanitize input data
 */
function sanitizeData(data: unknown): unknown {
  if (typeof data === 'string') {
    return data
      .replace(/<script[^>]*>.*?<\/script>/gi, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+\s*=/gi, '')
      .replace(/<iframe[^>]*>.*?<\/iframe>/gi, '')
      .replace(/<object[^>]*>.*?<\/object>/gi, '')
      .replace(/<embed[^>]*>.*?<\/embed>/gi, '')
      .replace(/[;&|`$(){}[\]]/g, '')
      .trim();
  } else if (Array.isArray(data)) {
    return data.map(sanitizeData);
  } else if (typeof data === 'object' && data !== null) {
    const sanitized: Record<string, unknown> = {};
    Object.entries(data).forEach(([key, value]) => {
      sanitized[key] = sanitizeData(value);
    });
    return sanitized;
  }
  return data;
}

/**
 * Log security threats
 */
function logSecurityThreats(threats: SecurityThreat[], request: NextRequest) {
  if (threats.length === 0) return;

  const criticalThreats = threats.filter(t => t.level === ThreatLevel.CRITICAL);
  const highThreats = threats.filter(t => t.level === ThreatLevel.HIGH);

  if (criticalThreats.length > 0 || highThreats.length > 0) {
    logger.error('Security threats detected', {
      url: request.url,
      method: request.method,
      userAgent: request.headers.get('user-agent'),
      ip: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip'),
      threats: threats.map(t => ({
        type: t.type,
        level: t.level,
        field: t.field,
        pattern: t.pattern
      }))
    });
  } else {
    logger.warn('Low-level security threats detected', {
      url: request.url,
      threatCount: threats.length,
      types: [...new Set(threats.map(t => t.type))]
    });
  }
}

/**
 * Create validation middleware for API routes
 */
export function createValidationMiddleware<T>(
  schema: z.ZodSchema<T>,
  options: {
    sanitize?: boolean;
    detectThreats?: boolean;
    blockOnThreats?: boolean;
    logThreats?: boolean;
  } = {}
) {
  const {
    sanitize = true,
    detectThreats = true,
    blockOnThreats = true,
    logThreats = true
  } = options;

  return async (request: NextRequest): Promise<{ data: T; threats: SecurityThreat[] } | NextResponse> => {
    try {
      // Parse request body
      let body: unknown;
      const contentType = request.headers.get('content-type');

      if (contentType?.includes('application/json')) {
        body = await request.json();
      } else if (contentType?.includes('application/x-www-form-urlencoded')) {
        const formData = await request.formData();
        body = Object.fromEntries(formData.entries());
      } else {
        body = {};
      }

      // Detect security threats
      let threats: SecurityThreat[] = [];
      if (detectThreats) {
        threats = detectSecurityThreats(body);

        if (logThreats) {
          logSecurityThreats(threats, request);
        }

        // Block request if critical threats detected
        if (blockOnThreats) {
          const criticalThreats = threats.filter(t => t.level === ThreatLevel.CRITICAL);
          if (criticalThreats.length > 0) {
            return NextResponse.json(
              {
                error: 'Request blocked due to security threats',
                code: 'SECURITY_THREAT_DETECTED'
              },
              { status: 400 }
            );
          }
        }
      }

      // Sanitize data if enabled
      let processedBody = body;
      if (sanitize) {
        processedBody = sanitizeData(body);
      }

      // Validate with Zod schema
      const result = schema.safeParse(processedBody);
      if (!result.success) {
        logger.warn('Validation failed', {
          url: request.url,
          errors: result.error.errors,
          body: JSON.stringify(processedBody).substring(0, 500)
        });

        return NextResponse.json(
          {
            error: 'Validation failed',
            details: result.error.errors.map(e => ({
              field: e.path.join('.'),
              message: e.message
            }))
          },
          { status: 400 }
        );
      }

      return { data: result.data, threats };
    } catch (error) {
      logger.error('Validation middleware error', error);
      return NextResponse.json(
        { error: 'Internal validation error' },
        { status: 500 }
      );
    }
  };
}

/**
 * Quick validation helper for simple schemas
 */
export async function validateRequest<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): Promise<T | NextResponse> {
  const middleware = createValidationMiddleware(schema);
  const result = await middleware(request);

  if (result instanceof NextResponse) {
    return result;
  }

  return result.data;
}

/**
 * Validation middleware for query parameters
 */
export function validateQueryParams<T>(
  request: NextRequest,
  schema: z.ZodSchema<T>
): T | NextResponse {
  try {
    const url = new URL(request.url);
    const params = Object.fromEntries(url.searchParams.entries());

    const result = schema.safeParse(params);
    if (!result.success) {
      return NextResponse.json(
        {
          error: 'Invalid query parameters',
          details: result.error.errors.map(e => ({
            field: e.path.join('.'),
            message: e.message
          }))
        },
        { status: 400 }
      );
    }

    return result.data;
  } catch (error) {
    logger.error('Query validation error', error);
    return NextResponse.json(
      { error: 'Query validation failed' },
      { status: 500 }
    );
  }
}

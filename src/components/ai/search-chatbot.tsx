'use client';

/**
 * Search Chatbot Component
 *
 * Provides a conversational interface for the AI search agent with
 * persistent memory using Mem0 and context-aware interactions.
 */

import React, { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { toast } from 'sonner';
import {
  Send,
  Loader2,
  Bot,
  User,
  Brain,
  Search,
  MessageSquare,
  Clock,
  Zap,
  History,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: string;
  metadata?: {
    toolsUsed?: string[];
    searchResults?: Record<string, unknown>;
    memoryContextUsed?: number;
  };
}

interface ChatSession {
  sessionId: string;
  messages: Message[];
  lastActivity: string;
  isActive: boolean;
}

export function SearchChatbot() {
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [message, setMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [includeSearch, setIncludeSearch] = useState(true);

  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [currentSession?.messages]);

  // Focus input on mount
  useEffect(() => {
    inputRef.current?.focus();
  }, []);

  const startNewSession = () => {
    const newSession: ChatSession = {
      sessionId: `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      messages: [],
      lastActivity: new Date().toISOString(),
      isActive: true,
    };
    setCurrentSession(newSession);
    toast.success('New conversation started');
  };

  const sendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim()) {
      toast.error('Please enter a message');
      return;
    }

    if (!currentSession) {
      startNewSession();
      return;
    }

    const userMessage: Message = {
      id: `msg_${Date.now()}_user`,
      role: 'user',
      content: message.trim(),
      timestamp: new Date().toISOString(),
    };

    // Add user message to current session
    setCurrentSession(prev => prev ? {
      ...prev,
      messages: [...prev.messages, userMessage],
      lastActivity: new Date().toISOString(),
    } : null);

    setMessage('');
    setIsLoading(true);

    try {
      const response = await fetch('/api/ai/search-chatbot', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message: userMessage.content,
          sessionId: currentSession.sessionId,
          conversationHistory: currentSession.messages.map(msg => ({
            role: msg.role,
            content: msg.content,
          })),
          includeSearch,
        }),
      });

      const data = await response.json();

      if (data.success) {
        const assistantMessage: Message = {
          id: `msg_${Date.now()}_assistant`,
          role: 'assistant',
          content: data.response,
          timestamp: new Date().toISOString(),
          metadata: {
            toolsUsed: data.metadata.toolsUsed,
            searchResults: data.metadata.searchResults,
            memoryContextUsed: data.metadata.memoryContextUsed,
          },
        };

        setCurrentSession(prev => prev ? {
          ...prev,
          messages: [...prev.messages, assistantMessage],
          lastActivity: new Date().toISOString(),
        } : null);

        toast.success('Response received');
      } else {
        toast.error(data.error || 'Failed to get response');
      }
    } catch (error) {
      console.error('Chat error:', error);
      toast.error('Failed to send message');
    } finally {
      setIsLoading(false);
      inputRef.current?.focus();
    }
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString();
  };

  const getToolIcon = (tool: string) => {
    switch (tool) {
      case 'searchExecutionTool':
        return <Search className="h-3 w-3" />;
      case 'memoryRetrievalTool':
        return <Brain className="h-3 w-3" />;
      case 'memoryStorageTool':
        return <History className="h-3 w-3" />;
      default:
        return <Zap className="h-3 w-3" />;
    }
  };

  const getToolColor = (tool: string) => {
    switch (tool) {
      case 'searchExecutionTool':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'memoryRetrievalTool':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'memoryStorageTool':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className="w-full max-w-4xl mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold flex items-center justify-center gap-2">
          <MessageSquare className="h-8 w-8" />
          AI Search Assistant
        </h1>
        <p className="text-muted-foreground">
          Conversational AI with search capabilities and persistent memory
        </p>
      </div>

      {/* Chat Interface */}
      <Card className="h-[600px] flex flex-col">
        <CardHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-lg">
                {currentSession ? `Session: ${currentSession.sessionId.split('_')[1]}` : 'No Active Session'}
              </CardTitle>
              <CardDescription>
                {currentSession
                  ? `${currentSession.messages.length} messages • Last activity: ${formatTimestamp(currentSession.lastActivity)}`
                  : 'Start a new conversation to begin'
                }
              </CardDescription>
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIncludeSearch(!includeSearch)}
                className={includeSearch ? 'bg-blue-50 border-blue-200' : ''}
              >
                <Search className="h-4 w-4 mr-1" />
                Search {includeSearch ? 'On' : 'Off'}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={startNewSession}
              >
                <RefreshCw className="h-4 w-4 mr-1" />
                New Chat
              </Button>
            </div>
          </div>
        </CardHeader>

        <Separator />

        {/* Messages Area */}
        <CardContent className="flex-1 p-0">
          <ScrollArea className="h-full p-4">
            {!currentSession ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-4">
                  <Bot className="h-16 w-16 mx-auto text-muted-foreground" />
                  <div>
                    <h3 className="text-lg font-semibold">Welcome to AI Search Assistant</h3>
                    <p className="text-muted-foreground">
                      Start a conversation to access AI-powered search and persistent memory
                    </p>
                  </div>
                  <Button onClick={startNewSession}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Start New Conversation
                  </Button>
                </div>
              </div>
            ) : currentSession.messages.length === 0 ? (
              <div className="flex items-center justify-center h-full">
                <div className="text-center space-y-2">
                  <Bot className="h-12 w-12 mx-auto text-muted-foreground" />
                  <p className="text-muted-foreground">
                    Send a message to start the conversation
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {currentSession.messages.map((msg) => (
                  <div
                    key={msg.id}
                    className={`flex gap-3 ${msg.role === 'user' ? 'justify-end' : 'justify-start'}`}
                  >
                    {msg.role === 'assistant' && (
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                          <Bot className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                        </div>
                      </div>
                    )}

                    <div className={`max-w-[80%] ${msg.role === 'user' ? 'order-1' : ''}`}>
                      <div
                        className={`rounded-lg p-3 ${
                          msg.role === 'user'
                            ? 'bg-blue-600 text-white'
                            : 'bg-muted'
                        }`}
                      >
                        <div className="whitespace-pre-wrap text-sm">
                          {msg.content}
                        </div>
                      </div>

                      <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        {formatTimestamp(msg.timestamp)}

                        {msg.metadata?.toolsUsed && msg.metadata.toolsUsed.length > 0 && (
                          <>
                            <Separator orientation="vertical" className="h-3" />
                            <div className="flex items-center gap-1">
                              {msg.metadata.toolsUsed.map((tool, index) => (
                                <Badge key={index} variant="secondary" className={`text-xs ${getToolColor(tool)}`}>
                                  {getToolIcon(tool)}
                                  <span className="ml-1">
                                    {tool.replace('Tool', '').replace(/([A-Z])/g, ' $1').trim()}
                                  </span>
                                </Badge>
                              ))}
                            </div>
                          </>
                        )}

                        {msg.metadata?.memoryContextUsed && msg.metadata.memoryContextUsed > 0 && (
                          <>
                            <Separator orientation="vertical" className="h-3" />
                            <span className="flex items-center gap-1">
                              <Brain className="h-3 w-3" />
                              {msg.metadata.memoryContextUsed} memories used
                            </span>
                          </>
                        )}

                        {(() => {
                          const citations = msg.metadata?.searchResults?.citations;
                          return citations && Array.isArray(citations) && citations.length > 0 ? (
                            <>
                              <Separator orientation="vertical" className="h-3" />
                              <span className="flex items-center gap-1">
                                <ExternalLink className="h-3 w-3" />
                                {citations.length} sources
                              </span>
                            </>
                          ) : null;
                        })()}
                      </div>
                    </div>

                    {msg.role === 'user' && (
                      <div className="flex-shrink-0">
                        <div className="w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center">
                          <User className="h-4 w-4 text-gray-600 dark:text-gray-300" />
                        </div>
                      </div>
                    )}
                  </div>
                ))}

                {isLoading && (
                  <div className="flex gap-3 justify-start">
                    <div className="flex-shrink-0">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                        <Bot className="h-4 w-4 text-blue-600 dark:text-blue-300" />
                      </div>
                    </div>
                    <div className="bg-muted rounded-lg p-3">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Thinking...
                      </div>
                    </div>
                  </div>
                )}

                <div ref={messagesEndRef} />
              </div>
            )}
          </ScrollArea>
        </CardContent>

        <Separator />

        {/* Input Area */}
        <div className="p-4">
          <form onSubmit={sendMessage} className="flex gap-2">
            <Input
              ref={inputRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder={currentSession ? "Type your message..." : "Start a new conversation first"}
              disabled={isLoading || !currentSession}
              className="flex-1"
              maxLength={2000}
            />
            <Button
              type="submit"
              disabled={isLoading || !message.trim() || !currentSession}
              size="icon"
            >
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Send className="h-4 w-4" />
              )}
            </Button>
          </form>

          {currentSession && (
            <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
              <span>
                Session: {currentSession.sessionId} • {currentSession.messages.length} messages
              </span>
              <span>
                {message.length}/2000 characters
              </span>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}

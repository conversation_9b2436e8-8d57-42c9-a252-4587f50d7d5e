'use client';

import { <PERSON>actNode, useMemo, useCallback } from 'react';
import { ConnectionProvider, WalletProvider } from '@solana/wallet-adapter-react';
import { WalletAdapter, WalletAdapterNetwork, WalletError } from '@solana/wallet-adapter-base';
import { WalletModalProvider } from '@solana/wallet-adapter-react-ui';
import {
  PhantomWalletAdapter,
  SolflareWalletAdapter,
  TorusWalletAdapter,
  LedgerWalletAdapter,
} from '@solana/wallet-adapter-wallets';
import { WalletConnectWalletAdapter } from '@solana/wallet-adapter-walletconnect';
import { useNetwork, getWalletAdapterNetwork } from '@/contexts/network-context';

// Import Solana wallet adapter styles
import '@solana/wallet-adapter-react-ui/styles.css';

interface Web3ProviderProps {
  children: ReactNode;
}

// Internal component that uses the network context
function Web3ProviderInternal({ children }: Web3ProviderProps) {
  // Get the current network from context
  const { currentNetwork, networkConfig } = useNetwork();

  // Convert to WalletAdapterNetwork
  const network = getWalletAdapterNetwork(currentNetwork);

  // Get the RPC endpoint
  const endpoint = useMemo(() => {
    console.log('🔗 Solana connection initialized:', {
      network: currentNetwork,
      url: networkConfig.url,
      chainId: networkConfig.chainId
    });
    return networkConfig.url;
  }, [currentNetwork, networkConfig]);

  // Error handler for wallet errors
  const onError = useCallback((error: WalletError) => {
    console.error('🚨 Wallet error occurred:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });

    // Handle specific wallet errors
    switch (error.name) {
      case 'WalletNotReadyError':
        console.warn('⚠️ Wallet not ready - user may need to install or unlock wallet');
        break;
      case 'WalletConnectionError':
        console.warn('⚠️ Wallet connection failed - retrying may help');
        break;
      case 'WalletDisconnectedError':
        console.warn('⚠️ Wallet disconnected unexpectedly');
        break;
      default:
        console.error('❌ Unknown wallet error:', error);
    }
  }, []);

  // Configure supported wallets
  const wallets = useMemo(() => {
    console.log('🔧 Configuring Solana wallets for network:', currentNetwork);

    try {
      const walletAdapters: WalletAdapter[] = [
        new PhantomWalletAdapter(),
        new SolflareWalletAdapter({ network }),
        new TorusWalletAdapter(),
        new LedgerWalletAdapter(),
      ];

      // Only add WalletConnect if project ID is available
      const projectId = process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID;
      if (projectId && projectId !== 'demo-project-id') {
        try {
          walletAdapters.push(
            new WalletConnectWalletAdapter({
              network: network as WalletAdapterNetwork.Mainnet | WalletAdapterNetwork.Devnet,
              options: {
                projectId,
                metadata: {
                  name: 'BuddyChip Copium',
                  description: 'Your Twitter Assistant with Solana Integration',
                  url: process.env.NEXT_PUBLIC_APP_URL || 'https://buddychip.app',
                  icons: ['https://buddychip.app/favicon.ico'],
                },
              },
            })
          );
          console.log('✅ WalletConnect adapter added successfully');
        } catch (wcError) {
          console.warn('⚠️ Failed to initialize WalletConnect adapter:', wcError);
        }
      } else {
        console.warn('⚠️ WalletConnect project ID not configured, skipping WalletConnect adapter');
      }

      return walletAdapters;
    } catch (error) {
      console.error('❌ Error configuring wallet adapters:', error);
      return [];
    }
  }, [network, currentNetwork]);

  console.log('🌐 Solana Web3Provider initialized:', {
    network: currentNetwork,
    endpoint,
    walletsCount: wallets.length,
  });

  return (
    <ConnectionProvider endpoint={endpoint}>
      <WalletProvider wallets={wallets} onError={onError} autoConnect>
        <WalletModalProvider>
          {children}
        </WalletModalProvider>
      </WalletProvider>
    </ConnectionProvider>
  );
}

// Main Web3Provider component that can be used without network context
export function Web3Provider({ children }: Web3ProviderProps) {
  return <Web3ProviderInternal>{children}</Web3ProviderInternal>;
}

/**
 * Loading Provider
 * 
 * Global provider for managing loading states across the application,
 * including compilation loading screens and page transitions.
 */

"use client";

import React, { createContext, useContext, useState, useEffect, ReactNode } from "react";
import { CompilationLoading } from "@/components/ui/compilation-loading";
import { logger } from "@/lib/utils/logger";

interface LoadingContextType {
  /** Whether the app is currently loading */
  isLoading: boolean;
  /** Whether compilation is in progress */
  isCompiling: boolean;
  /** Current loading message */
  message: string;
  /** Show loading screen */
  showLoading: (message?: string) => void;
  /** Hide loading screen */
  hideLoading: () => void;
  /** Show compilation loading */
  showCompilation: (message?: string) => void;
  /** Hide compilation loading */
  hideCompilation: () => void;
}

const LoadingContext = createContext<LoadingContextType | undefined>(undefined);

interface LoadingProviderProps {
  children: ReactNode;
  /** Whether to show loading on initial mount */
  showInitialLoading?: boolean;
  /** Initial loading message */
  initialMessage?: string;
}

export function LoadingProvider({
  children,
  showInitialLoading = false,
  initialMessage = "Loading...",
}: LoadingProviderProps) {
  const [isLoading, setIsLoading] = useState(showInitialLoading);
  const [isCompiling, setIsCompiling] = useState(false);
  const [message, setMessage] = useState(initialMessage);

  // Auto-hide loading after initial mount
  useEffect(() => {
    if (showInitialLoading) {
      const timer = setTimeout(() => {
        setIsLoading(false);
        logger.info("Initial loading completed", { duration: "3000ms" });
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [showInitialLoading]);

  // Detect slow page loads and show compilation loading
  useEffect(() => {
    let slowLoadTimer: NodeJS.Timeout;

    if (isLoading) {
      slowLoadTimer = setTimeout(() => {
        if (isLoading) {
          setIsCompiling(true);
          setMessage("Compiling page components...");
          logger.info("Slow page load detected - showing compilation loading");
        }
      }, 5000); // Show compilation loading after 5 seconds
    }

    return () => {
      if (slowLoadTimer) {
        clearTimeout(slowLoadTimer);
      }
    };
  }, [isLoading]);

  const showLoading = (newMessage = "Loading...") => {
    setIsLoading(true);
    setMessage(newMessage);
    logger.debug("Loading started", { message: newMessage });
  };

  const hideLoading = () => {
    setIsLoading(false);
    setIsCompiling(false);
    logger.debug("Loading stopped");
  };

  const showCompilation = (newMessage = "Compiling...") => {
    setIsCompiling(true);
    setMessage(newMessage);
    logger.debug("Compilation loading started", { message: newMessage });
  };

  const hideCompilation = () => {
    setIsCompiling(false);
    logger.debug("Compilation loading stopped");
  };

  const contextValue: LoadingContextType = {
    isLoading,
    isCompiling,
    message,
    showLoading,
    hideLoading,
    showCompilation,
    hideCompilation,
  };

  return (
    <LoadingContext.Provider value={contextValue}>
      {children}
      
      {/* Compilation Loading Overlay */}
      <CompilationLoading
        isVisible={isCompiling}
        message={message}
        estimatedTime={20}
      />
    </LoadingContext.Provider>
  );
}

/**
 * Hook to use the loading context
 */
export function useLoading() {
  const context = useContext(LoadingContext);
  
  if (context === undefined) {
    throw new Error("useLoading must be used within a LoadingProvider");
  }
  
  return context;
}

/**
 * Hook for managing async operations with automatic loading states
 */
export function useAsyncWithLoading() {
  const { showLoading, hideLoading } = useLoading();

  const executeWithLoading = async <T,>(
    operation: () => Promise<T>,
    loadingMessage = "Processing..."
  ): Promise<T> => {
    try {
      showLoading(loadingMessage);
      const result = await operation();
      return result;
    } catch (error) {
      logger.error("Async operation failed", error);
      throw error;
    } finally {
      hideLoading();
    }
  };

  return { executeWithLoading };
}

/**
 * Component wrapper that shows loading during component initialization
 */
export function withLoading<P extends object>(
  Component: React.ComponentType<P>,
  loadingMessage = "Loading component..."
) {
  return function LoadingWrapper(props: P) {
    const [isComponentLoading, setIsComponentLoading] = useState(true);
    const { showLoading, hideLoading } = useLoading();

    useEffect(() => {
      showLoading(loadingMessage);
      
      // Simulate component initialization time
      const timer = setTimeout(() => {
        setIsComponentLoading(false);
        hideLoading();
      }, 1000);

      return () => {
        clearTimeout(timer);
        hideLoading();
      };
    }, [showLoading, hideLoading]);

    if (isComponentLoading) {
      return null; // Loading is handled by the provider
    }

    return <Component {...props} />;
  };
}

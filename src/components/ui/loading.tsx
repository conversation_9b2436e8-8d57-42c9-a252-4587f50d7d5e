/**
 * Loading Component
 *
 * A reusable loading component with different variants for different contexts.
 * Uses Lucide icons and Tailwind CSS for styling.
 */

"use client";

import React from "react";
import { Loader2, RefreshCw } from "lucide-react";
import { cn } from "@/lib/utils";

export type LoadingVariant = "default" | "page" | "card" | "inline" | "button" | "compilation";

export interface LoadingProps {
  /** The variant of the loading component */
  variant?: LoadingVariant;
  /** Text to display alongside the loading indicator */
  text?: string;
  /** Additional CSS classes */
  className?: string;
  /** Size of the loading indicator in pixels */
  size?: number;
}

/**
 * Loading component that displays a spinner with optional text
 * Available variants: default, page, card, inline, button
 */
export function Loading({
  variant = "default",
  text,
  className,
  size,
}: LoadingProps) {
  // Define variant-specific styles
  const variantStyles = {
    default: "text-primary",
    page: "fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm z-50",
    card: "flex items-center justify-center p-6 w-full",
    inline: "inline-flex items-center",
    button: "inline-flex items-center justify-center",
    compilation: "fixed inset-0 flex items-center justify-center bg-background/95 backdrop-blur-md z-50",
  };

  // Define variant-specific sizes
  const variantSizes = {
    default: 24,
    page: 40,
    card: 32,
    inline: 16,
    button: 16,
    compilation: 48,
  };

  // Use provided size or default for the variant
  const iconSize = size || variantSizes[variant];

  return (
    <div className={cn(variantStyles[variant], className)}>
      <div className="flex flex-col items-center justify-center gap-2">
        <Loader2
          className="animate-spin"
          size={iconSize}
          aria-hidden="true"
        />
        {text && (
          <p className={cn(
            "text-center font-medium",
            variant === "page" ? "text-lg" : "text-sm"
          )}>
            {text}
          </p>
        )}
      </div>
    </div>
  );
}

/**
 * Button loading component that displays a spinner inside a button
 */
export function ButtonLoading({ className, size = 16 }: { className?: string, size?: number }) {
  return (
    <Loader2
      className={cn("animate-spin", className)}
      size={size}
      aria-hidden="true"
    />
  );
}

/**
 * Refresh loading component that displays a rotating refresh icon
 */
export function RefreshLoading({ className, size = 16 }: { className?: string, size?: number }) {
  return (
    <RefreshCw
      className={cn("animate-spin", className)}
      size={size}
      aria-hidden="true"
    />
  );
}

/**
 * Compilation Loading Component
 *
 * A specialized loading component that shows during Next.js compilation
 * with helpful information and progress indicators.
 */

"use client";

import React, { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Loader2, Code, Clock } from "lucide-react";
import { cn } from "@/lib/utils";

interface CompilationLoadingProps {
  /** Whether the loading screen is visible */
  isVisible?: boolean;
  /** Custom message to display */
  message?: string;
  /** Estimated compilation time in seconds */
  estimatedTime?: number;
  /** Additional CSS classes */
  className?: string;
}

const compilationTips = [
  "💡 Turbopack is optimizing your code for faster development",
  "🚀 First-time compilation takes longer but subsequent builds are faster",
  "⚡ Hot reloading will make future changes instant",
  "🎯 Your app is being optimized for the best performance",
  "🔧 Dependencies are being bundled and optimized",
];

const compilationSteps = [
  { label: "Analyzing dependencies", duration: 2000 },
  { label: "Compiling TypeScript", duration: 3000 },
  { label: "Bundling components", duration: 4000 },
  { label: "Optimizing assets", duration: 2000 },
  { label: "Generating pages", duration: 3000 },
  { label: "Finalizing build", duration: 1000 },
];

export function CompilationLoading({
  isVisible = true,
  message = "Compiling your application...",
  estimatedTime = 15,
  className,
}: CompilationLoadingProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [currentTip, setCurrentTip] = useState(0);
  const [elapsedTime, setElapsedTime] = useState(0);

  // Cycle through compilation steps
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setCurrentStep((prev) => (prev + 1) % compilationSteps.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [isVisible]);

  // Cycle through tips
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setCurrentTip((prev) => (prev + 1) % compilationTips.length);
    }, 5000);

    return () => clearInterval(interval);
  }, [isVisible]);

  // Track elapsed time
  useEffect(() => {
    if (!isVisible) return;

    const interval = setInterval(() => {
      setElapsedTime((prev) => prev + 1);
    }, 1000);

    return () => clearInterval(interval);
  }, [isVisible]);

  if (!isVisible) return null;

  const progress = Math.min((elapsedTime / estimatedTime) * 100, 95);

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className={cn(
          "fixed inset-0 bg-background/98 backdrop-blur-md z-50 flex items-center justify-center",
          className
        )}
      >
        <div className="max-w-lg w-full mx-auto p-8 space-y-8">
          {/* Main Loading Animation */}
          <motion.div
            initial={{ scale: 0.9, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ duration: 0.5 }}
            className="text-center space-y-6"
          >
            {/* Animated Compilation Icon */}
            <div className="relative mx-auto w-24 h-24">
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
                className="absolute inset-0 rounded-full border-4 border-primary/20 border-t-primary"
              />
              <motion.div
                animate={{ rotate: -360 }}
                transition={{ duration: 6, repeat: Infinity, ease: "linear" }}
                className="absolute inset-2 rounded-full border-2 border-secondary/30 border-b-secondary"
              />
              <div className="absolute inset-4 rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 flex items-center justify-center">
                <Code className="w-8 h-8 text-primary" />
              </div>
            </div>

            {/* Title and Message */}
            <div className="space-y-2">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                BuddyChip
              </h2>
              <p className="text-muted-foreground">{message}</p>
            </div>
          </motion.div>

          {/* Current Step */}
          <motion.div
            key={`step-${currentStep}`}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            transition={{ duration: 0.3 }}
            className="flex items-center justify-center space-x-3 p-4 rounded-lg bg-muted/50"
          >
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
            >
              <Loader2 className="w-5 h-5 text-primary" />
            </motion.div>
            <span className="text-foreground font-medium">
              {compilationSteps[currentStep].label}
            </span>
          </motion.div>

          {/* Progress Bar */}
          <div className="space-y-2">
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>Progress</span>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4" />
                <span>{elapsedTime}s / ~{estimatedTime}s</span>
              </div>
            </div>

            <div className="w-full h-2 bg-muted rounded-full overflow-hidden">
              <motion.div
                initial={{ width: "0%" }}
                animate={{ width: `${progress}%` }}
                transition={{ duration: 0.5, ease: "easeOut" }}
                className="h-full bg-gradient-to-r from-primary to-secondary rounded-full"
              />
            </div>
          </div>

          {/* Rotating Tips */}
          <motion.div
            key={`tip-${currentTip}`}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.5 }}
            className="text-center p-4 rounded-lg bg-primary/5 border border-primary/10"
          >
            <p className="text-sm text-muted-foreground">
              {compilationTips[currentTip]}
            </p>
          </motion.div>

          {/* Performance Note */}
          {elapsedTime > estimatedTime && (
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              className="text-center p-3 rounded-lg bg-yellow-500/10 border border-yellow-500/20"
            >
              <p className="text-sm text-yellow-600 dark:text-yellow-400">
                Taking longer than expected? This is normal for the first compilation.
              </p>
            </motion.div>
          )}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

export default CompilationLoading;
